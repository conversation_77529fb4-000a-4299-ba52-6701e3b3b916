.class public final LZ8/B;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LV8/a;


# static fields
.field public static final a:LZ8/B;

.field public static final b:LZ8/T;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, LZ8/B;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LZ8/B;->a:LZ8/B;

    .line 7
    .line 8
    new-instance v0, LZ8/T;

    .line 9
    .line 10
    sget-object v1, LX8/c;->A:LX8/c;

    .line 11
    .line 12
    const-string v2, "kotlin.Int"

    .line 13
    .line 14
    invoke-direct {v0, v2, v1}, LZ8/T;-><init>(Ljava/lang/String;LX8/d;)V

    .line 15
    .line 16
    .line 17
    sput-object v0, LZ8/B;->b:LZ8/T;

    .line 18
    .line 19
    return-void
.end method


# virtual methods
.method public final b(LY8/b;)Ljava/lang/Object;
    .locals 1

    .line 1
    const-string v0, "decoder"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lt7/m;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-interface {p1}, LY8/b;->s()I

    .line 7
    .line 8
    .line 9
    move-result p1

    .line 10
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    return-object p1
.end method

.method public final c()LX8/e;
    .locals 1

    .line 1
    sget-object v0, LZ8/B;->b:LZ8/T;

    .line 2
    .line 3
    return-object v0
.end method
