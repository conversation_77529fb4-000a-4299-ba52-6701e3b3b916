.class public final Lz/F;
.super Ld0/k;
.source "SourceFile"


# instance fields
.field public G:LC/l;

.field public H:LC/d;


# virtual methods
.method public final n0(LC/l;LC/k;)V
    .locals 3

    .line 1
    iget-boolean v0, p0, Ld0/k;->F:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Ld0/k;->b0()LM8/z;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    new-instance v1, Lz/E;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-direct {v1, p1, p2, v2}, Lz/E;-><init>(LC/l;LC/k;Lk7/d;)V

    .line 13
    .line 14
    .line 15
    const/4 p1, 0x3

    .line 16
    const/4 p2, 0x0

    .line 17
    invoke-static {v0, v2, p2, v1, p1}, LM8/A;->s(LM8/z;Lk7/i;ILs7/n;I)LM8/t0;

    .line 18
    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    invoke-virtual {p1, p2}, LC/l;->b(LC/k;)V

    .line 22
    .line 23
    .line 24
    :goto_0
    return-void
.end method
