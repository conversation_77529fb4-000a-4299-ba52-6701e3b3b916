.class public final Lz/Q;
.super Lm7/c;
.source "SourceFile"


# instance fields
.field public t:Lz/V;

.field public u:LC/h;

.field public synthetic v:Ljava/lang/Object;

.field public final synthetic w:Lz/V;

.field public x:I


# direct methods
.method public constructor <init>(Lz/V;Lk7/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lz/Q;->w:Lz/V;

    .line 2
    .line 3
    invoke-direct {p0, p2}, Lm7/c;-><init>(Lk7/d;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iput-object p1, p0, Lz/Q;->v:Ljava/lang/Object;

    .line 2
    .line 3
    iget p1, p0, Lz/Q;->x:I

    .line 4
    .line 5
    const/high16 v0, -0x80000000

    .line 6
    .line 7
    or-int/2addr p1, v0

    .line 8
    iput p1, p0, Lz/Q;->x:I

    .line 9
    .line 10
    iget-object p1, p0, Lz/Q;->w:Lz/V;

    .line 11
    .line 12
    invoke-virtual {p1, p0}, Lz/V;->n0(Lk7/d;)Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    return-object p1
.end method
