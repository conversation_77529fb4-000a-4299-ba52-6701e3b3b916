.class public abstract LZ7/r;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:LJ7/j;

.field public static final b:LJ7/j;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, LJ7/j;

    .line 2
    .line 3
    sget-object v1, LR7/x;->p:Lh8/c;

    .line 4
    .line 5
    const-string v2, "ENHANCED_NULLABILITY_ANNOTATION"

    .line 6
    .line 7
    invoke-static {v1, v2}, Lt7/m;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 8
    .line 9
    .line 10
    invoke-direct {v0, v1}, LJ7/j;-><init>(Lh8/c;)V

    .line 11
    .line 12
    .line 13
    sput-object v0, LZ7/r;->a:LJ7/j;

    .line 14
    .line 15
    new-instance v0, LJ7/j;

    .line 16
    .line 17
    sget-object v1, LR7/x;->q:Lh8/c;

    .line 18
    .line 19
    const-string v2, "ENHANCED_MUTABILITY_ANNOTATION"

    .line 20
    .line 21
    invoke-static {v1, v2}, Lt7/m;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    invoke-direct {v0, v1}, LJ7/j;-><init>(Lh8/c;)V

    .line 25
    .line 26
    .line 27
    sput-object v0, LZ7/r;->b:LJ7/j;

    .line 28
    .line 29
    return-void
.end method
