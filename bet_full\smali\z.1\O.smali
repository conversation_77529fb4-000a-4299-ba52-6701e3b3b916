.class public final Lz/O;
.super Ld0/k;
.source "SourceFile"

# interfaces
.implements Lz0/d;


# instance fields
.field public final G:Ls7/k;

.field public final H:Lz0/h;


# direct methods
.method public constructor <init>(LA/A0;)V
    .locals 2

    .line 1
    invoke-direct {p0}, Ld0/k;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lz/O;->G:Ls7/k;

    .line 5
    .line 6
    new-instance p1, Lm8/w;

    .line 7
    .line 8
    const/16 v0, 0x10

    .line 9
    .line 10
    invoke-direct {p1, v0, p0}, Lm8/w;-><init>(ILjava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    sget-object v0, Lz/M;->a:Lz0/g;

    .line 14
    .line 15
    new-instance v1, Lz0/h;

    .line 16
    .line 17
    invoke-direct {v1, v0}, Lz0/h;-><init>(Lz0/g;)V

    .line 18
    .line 19
    .line 20
    iget-object v0, v1, Lz0/h;->b:LQ/d0;

    .line 21
    .line 22
    invoke-virtual {v0, p1}, LQ/d0;->setValue(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    iput-object v1, p0, Lz/O;->H:Lz0/h;

    .line 26
    .line 27
    return-void
.end method


# virtual methods
.method public final synthetic e(Lz0/g;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lt7/k;->a(Lz0/d;Lz0/g;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final f()Lt1/D0;
    .locals 1

    .line 1
    iget-object v0, p0, Lz/O;->H:Lz0/h;

    .line 2
    .line 3
    return-object v0
.end method
