.class public final LZ7/m;
.super Lt7/o;
.source "SourceFile"

# interfaces
.implements Ls7/k;


# static fields
.field public static final u:LZ7/m;

.field public static final v:LZ7/m;

.field public static final w:LZ7/m;

.field public static final x:LZ7/m;


# instance fields
.field public final synthetic t:I


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, LZ7/m;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    invoke-direct {v0, v1, v2}, LZ7/m;-><init>(II)V

    .line 6
    .line 7
    .line 8
    sput-object v0, LZ7/m;->u:LZ7/m;

    .line 9
    .line 10
    new-instance v0, LZ7/m;

    .line 11
    .line 12
    const/4 v1, 0x1

    .line 13
    const/4 v2, 0x1

    .line 14
    invoke-direct {v0, v1, v2}, LZ7/m;-><init>(II)V

    .line 15
    .line 16
    .line 17
    sput-object v0, LZ7/m;->v:LZ7/m;

    .line 18
    .line 19
    new-instance v0, LZ7/m;

    .line 20
    .line 21
    const/4 v1, 0x1

    .line 22
    const/4 v2, 0x2

    .line 23
    invoke-direct {v0, v1, v2}, LZ7/m;-><init>(II)V

    .line 24
    .line 25
    .line 26
    sput-object v0, LZ7/m;->w:LZ7/m;

    .line 27
    .line 28
    new-instance v0, LZ7/m;

    .line 29
    .line 30
    const/4 v1, 0x1

    .line 31
    const/4 v2, 0x3

    .line 32
    invoke-direct {v0, v1, v2}, LZ7/m;-><init>(II)V

    .line 33
    .line 34
    .line 35
    sput-object v0, LZ7/m;->x:LZ7/m;

    .line 36
    .line 37
    return-void
.end method

.method public synthetic constructor <init>(II)V
    .locals 0

    .line 1
    iput p2, p0, LZ7/m;->t:I

    invoke-direct {p0, p1}, Lt7/o;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    const-string v2, "it"

    .line 4
    .line 5
    iget v3, p0, LZ7/m;->t:I

    .line 6
    .line 7
    packed-switch v3, :pswitch_data_0

    .line 8
    .line 9
    .line 10
    check-cast p1, LZ7/n;

    .line 11
    .line 12
    const-string v2, "$this$function"

    .line 13
    .line 14
    invoke-static {p1, v2}, Lt7/m;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 15
    .line 16
    .line 17
    const-string v2, "Spliterator"

    .line 18
    .line 19
    const-string v3, "java/util/"

    .line 20
    .line 21
    invoke-virtual {v3, v2}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    const/4 v3, 0x2

    .line 26
    new-array v3, v3, [LZ7/e;

    .line 27
    .line 28
    sget-object v4, LZ7/k;->b:LZ7/e;

    .line 29
    .line 30
    aput-object v4, v3, v1

    .line 31
    .line 32
    aput-object v4, v3, v0

    .line 33
    .line 34
    invoke-virtual {p1, v2, v3}, LZ7/n;->b(Ljava/lang/String;[LZ7/e;)V

    .line 35
    .line 36
    .line 37
    sget-object p1, Lf7/v;->a:Lf7/v;

    .line 38
    .line 39
    return-object p1

    .line 40
    :pswitch_0
    check-cast p1, Ly8/a0;

    .line 41
    .line 42
    invoke-static {p1, v2}, Lt7/m;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 43
    .line 44
    .line 45
    instance-of p1, p1, LW7/f;

    .line 46
    .line 47
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    return-object p1

    .line 52
    :pswitch_1
    check-cast p1, LI7/c;

    .line 53
    .line 54
    invoke-static {p1, v2}, Lt7/m;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    invoke-interface {p1}, LI7/b;->r()Ly8/v;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    invoke-static {p1}, Lt7/m;->c(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    return-object p1

    .line 65
    :pswitch_2
    check-cast p1, LI7/c;

    .line 66
    .line 67
    invoke-static {p1, v2}, Lt7/m;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 68
    .line 69
    .line 70
    invoke-interface {p1}, LI7/b;->P()LL7/d;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    invoke-static {p1}, Lt7/m;->c(Ljava/lang/Object;)V

    .line 75
    .line 76
    .line 77
    invoke-virtual {p1}, LL7/d;->b()Ly8/v;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    const-string v0, "getType(...)"

    .line 82
    .line 83
    invoke-static {p1, v0}, Lt7/m;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 84
    .line 85
    .line 86
    return-object p1

    .line 87
    :pswitch_3
    check-cast p1, Ly8/a0;

    .line 88
    .line 89
    invoke-virtual {p1}, Ly8/v;->Z()Ly8/K;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    invoke-interface {p1}, Ly8/K;->c()LI7/h;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    if-nez p1, :cond_0

    .line 98
    .line 99
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 100
    .line 101
    goto :goto_1

    .line 102
    :cond_0
    invoke-interface {p1}, LI7/k;->getName()Lh8/f;

    .line 103
    .line 104
    .line 105
    move-result-object v2

    .line 106
    sget-object v3, LH7/d;->f:Lh8/c;

    .line 107
    .line 108
    invoke-virtual {v3}, Lh8/c;->f()Lh8/f;

    .line 109
    .line 110
    .line 111
    move-result-object v4

    .line 112
    invoke-static {v2, v4}, Lt7/m;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 113
    .line 114
    .line 115
    move-result v2

    .line 116
    if-eqz v2, :cond_1

    .line 117
    .line 118
    invoke-static {p1}, Lo8/d;->c(LI7/k;)Lh8/c;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    invoke-static {p1, v3}, Lt7/m;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    move-result p1

    .line 126
    if-eqz p1, :cond_1

    .line 127
    .line 128
    goto :goto_0

    .line 129
    :cond_1
    move v0, v1

    .line 130
    :goto_0
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    :goto_1
    return-object p1

    .line 135
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
