.class public abstract Lz/M;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:Lz0/g;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, Lz/L;->u:Lz/L;

    .line 2
    .line 3
    invoke-static {v0}, Lt1/d0;->m(Ls7/a;)Lz0/g;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sput-object v0, Lz/M;->a:Lz0/g;

    .line 8
    .line 9
    return-void
.end method
