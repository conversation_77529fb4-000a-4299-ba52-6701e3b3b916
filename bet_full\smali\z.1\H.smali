.class public final Lz/H;
.super Lm7/j;
.source "SourceFile"

# interfaces
.implements Ls7/n;


# instance fields
.field public t:I

.field public final synthetic u:Lz/I;


# direct methods
.method public constructor <init>(Lz/I;Lk7/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lz/H;->u:Lz/I;

    .line 2
    .line 3
    const/4 p1, 0x2

    .line 4
    invoke-direct {p0, p1, p2}, Lm7/j;-><init>(ILk7/d;)V

    .line 5
    .line 6
    .line 7
    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lk7/d;)Lk7/d;
    .locals 1

    .line 1
    new-instance p1, Lz/H;

    .line 2
    .line 3
    iget-object v0, p0, Lz/H;->u:Lz/I;

    .line 4
    .line 5
    invoke-direct {p1, v0, p2}, Lz/H;-><init>(Lz/I;Lk7/d;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final invoke(Lja<PERSON>/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LM8/z;

    .line 2
    .line 3
    check-cast p2, Lk7/d;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Lz/H;->create(Ljava/lang/Object;Lk7/d;)Lk7/d;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    check-cast p1, Lz/H;

    .line 10
    .line 11
    sget-object p2, Lf7/v;->a:Lf7/v;

    .line 12
    .line 13
    invoke-virtual {p1, p2}, Lz/H;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    sget-object v0, Ll7/a;->t:Ll7/a;

    .line 2
    .line 3
    iget v1, p0, Lz/H;->t:I

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    if-eqz v1, :cond_1

    .line 7
    .line 8
    if-ne v1, v2, :cond_0

    .line 9
    .line 10
    invoke-static {p1}, Lf7/a;->e(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 15
    .line 16
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 17
    .line 18
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    throw p1

    .line 22
    :cond_1
    invoke-static {p1}, Lf7/a;->e(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    iget-object p1, p0, Lz/H;->u:Lz/I;

    .line 26
    .line 27
    iget-object p1, p1, Lz/I;->N:LI/f;

    .line 28
    .line 29
    iput v2, p0, Lz/H;->t:I

    .line 30
    .line 31
    const/4 v1, 0x0

    .line 32
    invoke-virtual {p1, v1, p0}, LI/f;->a(Lj0/d;Lk7/d;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    if-ne p1, v0, :cond_2

    .line 37
    .line 38
    return-object v0

    .line 39
    :cond_2
    :goto_0
    sget-object p1, Lf7/v;->a:Lf7/v;

    .line 40
    .line 41
    return-object p1
.end method
