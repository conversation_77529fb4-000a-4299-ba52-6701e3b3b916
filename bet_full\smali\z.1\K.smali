.class public final Lz/K;
.super Ld0/k;
.source "SourceFile"

# interfaces
.implements LA0/r0;
.implements Li0/n;


# instance fields
.field public G:Z


# virtual methods
.method public final synthetic S()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    return v0
.end method

.method public final synthetic T()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    return v0
.end method

.method public final p(LF0/i;)V
    .locals 4

    .line 1
    iget-boolean v0, p0, Lz/K;->G:Z

    .line 2
    .line 3
    sget-object v1, LF0/s;->a:[Lz7/u;

    .line 4
    .line 5
    sget-object v1, LF0/q;->k:LF0/t;

    .line 6
    .line 7
    sget-object v2, LF0/s;->a:[Lz7/u;

    .line 8
    .line 9
    const/4 v3, 0x4

    .line 10
    aget-object v2, v2, v3

    .line 11
    .line 12
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {v1, p1, v0}, LF0/t;->a(LF0/i;Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    new-instance v0, Lr8/s;

    .line 20
    .line 21
    const/16 v1, 0x10

    .line 22
    .line 23
    invoke-direct {v0, v1, p0}, Lr8/s;-><init>(ILjava/lang/Object;)V

    .line 24
    .line 25
    .line 26
    sget-object v1, LF0/h;->s:LF0/t;

    .line 27
    .line 28
    new-instance v2, LF0/a;

    .line 29
    .line 30
    const/4 v3, 0x0

    .line 31
    invoke-direct {v2, v3, v0}, LF0/a;-><init>(Ljava/lang/String;Lf7/c;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p1, v1, v2}, LF0/i;->u(LF0/t;Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    return-void
.end method
